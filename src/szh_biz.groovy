pipeline {
    agent any
    tools {
        maven 'Maven3.9.0'
        jdk 'JDK8'
    }

    parameters {
        choice(name: 'BRANCH_NAME', choices: ['dev', 'main', 'test','feature/geo'], description: '数字化 BIZ 分支选择')
    }


    stages {
        stage('Checkout Code') {
            steps {
                git branch: "${params.BRANCH_NAME}", credentialsId: 'GitLab-User-Root', url: 'http://***********:9600/ztkj/Szh-Biz.git'
                echo "分支是 ${params.BRANCH_NAME}"
            }
        }
        stage('Maven  Build') {
            steps {
                sh "mvn clean install"
                sh "mvn package"
            }
        }

        stage('Dockerfile Build And Push') {
            steps {
                sh "mvn dockerfile:build dockerfile:push"
            }
        }
    }
}
