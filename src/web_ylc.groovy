pipeline {
    agent any
    tools {
        nodejs 'Node16.20.0'
    }

    parameters {
        choice(name: 'PUBLISHING_ENVIRONMENT', choices: ['测试环境', '正式环境'], description: '发布环境选择')
        choice(name: 'INSTALL_DEPENDENCIES', choices: ['否', '是'], description: '是否需要重新安装依赖？(添加了新的第三方依赖，需要选择是)')
    }

    stages {
        stage('Checkout Code') {
            steps {
                git branch: "dev-coolThem", credentialsId: 'GitLab-User-Root', url: 'http://***********:9600/ztkj/myhsg_imcs_html.git'
            }
        }

        stage('Build') {
            steps {
                script {
                    try{
                        if (params.INSTALL_DEPENDENCIES == '是') {
                            sh 'npm install'
                        }
                        if (params.PUBLISHING_ENVIRONMENT == '测试环境') {
                            sh 'npm run ylc-build-dev'
                        }else {
                            sh 'npm run ylc-build-pro'
                        }
                        zip zipFile: 'dist.zip', dir: 'dist', archive: true
                    }catch (err){
                        echo "build有错误，但继续执行：${err}"
                    }
                }
            }
        }

        stage('Archive') {
            steps {
                archiveArtifacts artifacts: 'dist.zip', fingerprint: true
            }
        }

        stage('Upload to Server') {
            steps {
                script {
                    if (params.PUBLISHING_ENVIRONMENT == '测试环境') {
                        sshPublisher(
                                publishers: [
                                        sshPublisherDesc(
                                                configName: '111服务器',
                                                transfers: [
                                                        sshTransfer(
                                                                cleanRemote: false,
                                                                excludes: '',
                                                                execCommand: 'bash ~/jenkins_remote/project/ylc/web/script/installDev.sh',
                                                                execTimeout: 120000,
                                                                flatten: false,
                                                                makeEmptyDirs: false,
                                                                noDefaultExcludes: false,
                                                                patternSeparator: '[, ]+',
                                                                remoteDirectory: '/jenkins_remote/project/ylc/web/dev',
                                                                remoteDirectorySDF: false,
                                                                removePrefix: '',
                                                                sourceFiles: 'dist.zip'
                                                        )
                                                ],
                                                usePromotionTimestamp: false,
                                                useWorkspaceInPromotion: false,
                                                verbose: false
                                        )
                                ]
                        )
                    }else {
                        sshPublisher(
                                publishers: [
                                        sshPublisherDesc(
                                                configName: '103服务器',
                                                transfers: [
                                                        sshTransfer(
                                                                cleanRemote: false,
                                                                excludes: '',
                                                                execCommand: 'bash ~/jenkins_remote/project/web/ylc/script/installRelease.sh',
                                                                execTimeout: 120000,
                                                                flatten: false,
                                                                makeEmptyDirs: false,
                                                                noDefaultExcludes: false,
                                                                patternSeparator: '[, ]+',
                                                                remoteDirectory: '/jenkins_remote/project/web/ylc/release',
                                                                remoteDirectorySDF: false,
                                                                removePrefix: '',
                                                                sourceFiles: 'dist.zip'
                                                        )
                                                ],
                                                usePromotionTimestamp: false,
                                                useWorkspaceInPromotion: false,
                                                verbose: false
                                        )
                                ]
                        )
                    }
                }
            }
        }

    }
}
