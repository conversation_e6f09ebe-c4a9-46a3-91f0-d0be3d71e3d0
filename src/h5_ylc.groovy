pipeline {
    agent any

    parameters {
        choice(name: 'APP_TYPE', choices: ['领导端', '司机端'], description: '内嵌网页类型')
    }

    stages {
        stage('Checkout Code') {
            steps {
                cleanWs()
                git branch: "dev", credentialsId: 'GitLab-User-Root', url: 'http://***********:9600/ztkj/app-vehicle.git'
                echo "${params.APP_TYPE}"
                echo "${params.PUBLISHING_ENVIRONMENT}"
            }
        }

        stage('Modify Config') {
            //修改config.js的内容
            steps {
                script {
                    def httpUrl
                    def geoServer
                    def configJsPath = 'hybrid/html/js/config.js'
                    // 根据参数值设置httpUrl和geoServer的值
                    if (params.PUBLISHING_ENVIRONMENT == '测试环境') {
                        httpUrl = 'http://***********:6223'
                        geoServer = 'http://***********:6200/geoserver/ylc'
                    } else {
                        httpUrl = 'http://***********:8089'
                        geoServer = 'http://**************:5431/geoserver/ylc'
                    }

                    // 替换config.js中的变量值
                    sh "sed -i \"s|var httpUrl = .\\+|var httpUrl = \\'${httpUrl}\\';|\" ${configJsPath}"
                    sh "sed -i \"s|var geoServer = .\\+|var geoServer = \\'${geoServer}\\';|\" ${configJsPath}"
                }
            }
        }


        stage('Build') {
            steps {
                script {
                    if (params.APP_TYPE == '司机端') {
                        zip zipFile: 'dist.zip', dir: 'hybrid/html', archive: true
                    } else {
                        zip zipFile: 'dist.zip', dir: 'unpackage/dist/build/h5', archive: true
                    }
                }
            }
        }

        stage('Archive') {
            steps {
                archiveArtifacts artifacts: 'dist.zip', fingerprint: true
            }
        }

        stage('Upload to Server') {
            steps {
                script {
                    if (params.APP_TYPE == '司机端') {
                        sshPublisher(publishers: [sshPublisherDesc(configName: '103服务器',
                                transfers: [sshTransfer(cleanRemote: false,
                                        excludes: '',
                                        execCommand: 'bash ~/jenkins_remote/project/h5/ylc/script/installDriverRelease.sh',
                                        execTimeout: 120000,
                                        flatten: false,
                                        makeEmptyDirs: false,
                                        noDefaultExcludes: false,
                                        patternSeparator: '[, ]+',
                                        remoteDirectory: '/jenkins_remote/project/h5/ylc/release',
                                        remoteDirectorySDF: false,
                                        removePrefix: '',
                                        sourceFiles: 'dist.zip')],
                                usePromotionTimestamp: false,
                                useWorkspaceInPromotion: false,
                                verbose: false)])

                    } else {

                        sshPublisher(publishers: [sshPublisherDesc(configName: '103服务器',
                                transfers: [sshTransfer(cleanRemote: false,
                                        excludes: '',
                                        execCommand: 'bash ~/jenkins_remote/project/h5/ylc/script/installManagerRelease.sh',
                                        execTimeout: 120000,
                                        flatten: false,
                                        makeEmptyDirs: false,
                                        noDefaultExcludes: false,
                                        patternSeparator: '[, ]+',
                                        remoteDirectory: '/jenkins_remote/project/h5/ylc/release',
                                        remoteDirectorySDF: false,
                                        removePrefix: '',
                                        sourceFiles: 'dist.zip')],
                                usePromotionTimestamp: false,
                                useWorkspaceInPromotion: false,
                                verbose: false)])

                    }
                }
            }
        }

    }
}
