pipeline {
    agent any
    tools {
        maven 'Maven3.9.0'
        jdk 'JDK8'
    }

    parameters {
        choice(name: 'BRANCH_NAME', choices: [ 'dev-jc','main-jc'], description: 'BladeX-biz分支选择')
        choice(name: 'PUBLISHING_ENVIRONMENT', choices: ['dev','prod'], description: '运梁车BladeX环境选择')
    }

    stages {
        stage('Checkout Code') {
            steps {
                cleanWs()
                git branch: "${params.BRANCH_NAME}", credentialsId: 'GitLab-User-Root', url: 'http://***********:9600/ztkj/bladex-biz.git'
                echo "当前打包环境: ${params.PUBLISHING_ENVIRONMENT}"
            }
        }

        stage('Modified Dockerfile And pom') {
            steps {
                script {
                    def publishingEnvironment = params.PUBLISHING_ENVIRONMENT
                    echo "${publishingEnvironment}"
                    sh """
                        # 找到所有 Dockerfile，并存储在数组中
                        dockerfiles=\$(find . -maxdepth 3 -type f -name Dockerfile)
                        # 遍历 Dockerfile 数组并替换对应的配置
                        for dockerfile in \$dockerfiles; do
                            sed -i 's|CMD \\[\"--spring.profiles.active=.*\"\\]|CMD \\[\"--spring.profiles.active=${publishingEnvironment}\"\\]|' "\$dockerfile"
                        done
                        # 修改pom.xml中的docker.namespace节点值
                        if [ "${publishingEnvironment}" = "dev" ]; then
                            sed -i 's|<docker.namespace>.*</docker.namespace>|<docker.namespace>testzt</docker.namespace>|' pom.xml
                        elif [ "${publishingEnvironment}" = "prod" ]; then
                            sed -i 's|<docker.namespace>.*</docker.namespace>|<docker.namespace>prodzt</docker.namespace>|' pom.xml
                        fi
                    """
                }
            }
        }

        stage('Maven  Build') {
            steps {
                sh "mvn clean install"
                sh "mvn package"
            }
        }

        stage('Dockerfile Build And Push') {
            steps {
                sh "mvn dockerfile:build dockerfile:push"
            }
        }
    }
}
